import sys
import os
import numpy as np
import logging
from typing import List, Dict, Any

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from uq_methods.base import BaseUQMethod

log = logging.getLogger(__name__)

class NumSetsUQ(BaseUQMethod):
    """
    Estimates the sequence-level uncertainty of a language model following the method of
    "Number of Semantic Sets" as provided in the paper https://arxiv.org/abs/2305.19187.
    Works with both whitebox and blackbox models.
    """

    def __init__(self, model_name: str = "microsoft/deberta-large-mnli", use_unified_nli: bool = True, verbose: bool = False):
        self.model_name = model_name
        self.use_unified_nli = use_unified_nli
        self.verbose = verbose

        # 默认使用统一NLI系统
        if use_unified_nli:
            try:
                # 导入统一的NLI计算器
                from core.nli_calculator import CachedNLICalculator
                self.nli_calculator = CachedNLICalculator(model_name, verbose=False)
            except ImportError:
                print("Warning: Cannot import unified NLI calculator, falling back to independent implementation")
                self._init_independent_nli()
        else:
            # 向后兼容的独立实现
            self._init_independent_nli()

    def _init_independent_nli(self):
        """初始化独立的NLI实现（向后兼容）"""
        import torch
        from transformers import AutoTokenizer, AutoModelForSequenceClassification, DebertaV2Tokenizer

        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

        if self.model_name == "potsawee/deberta-v3-large-mnli":
            try:
                self.tokenizer = DebertaV2Tokenizer.from_pretrained(self.model_name)
            except:
                self.tokenizer = AutoTokenizer.from_pretrained(self.model_name, use_fast=False)
        else:
            self.tokenizer = AutoTokenizer.from_pretrained(self.model_name)

        self.model = AutoModelForSequenceClassification.from_pretrained(self.model_name).to(self.device)
        self.model.eval()

    def compute_semantic_matrices(self, responses: List[str]) -> Dict[str, np.ndarray]:
        """计算语义矩阵，返回entailment和contradiction分数矩阵"""
        n = len(responses)
        semantic_matrix_entail = np.zeros((n, n))
        semantic_matrix_contra = np.zeros((n, n))

        for i in range(n):
            for j in range(n):
                if i == j:
                    continue

                if self.use_unified_nli and hasattr(self, 'nli_calculator'):
                    # 使用统一的NLI系统获取分数
                    nli_result = self.nli_calculator.compute_nli_scores_cached(responses[i], responses[j])
                    semantic_matrix_entail[i, j] = nli_result.entailment
                    semantic_matrix_contra[i, j] = nli_result.contradiction
                else:
                    # 使用独立实现获取分数
                    scores = self._independent_pairwise_nli_scores(responses[i], responses[j])
                    semantic_matrix_entail[i, j] = scores['entailment']
                    semantic_matrix_contra[i, j] = scores['contradiction']

        return {
            'semantic_matrix_entail': semantic_matrix_entail,
            'semantic_matrix_contra': semantic_matrix_contra
        }

    def _independent_pairwise_nli_scores(self, text1: str, text2: str) -> Dict[str, float]:
        """独立的NLI推理实现，返回所有标签的分数（向后兼容）"""
        import torch

        inputs = self.tokenizer(
            text1, text2,
            return_tensors="pt",
            truncation=True,
            max_length=256
        ).to(self.device)

        with torch.no_grad():
            logits = self.model(**inputs).logits
            probs = torch.softmax(logits, dim=-1).cpu().numpy()[0]

        # Label mapping: 0=contradiction, 1=neutral, 2=entailment
        return {
            'contradiction': float(probs[0]),
            'neutral': float(probs[1]),
            'entailment': float(probs[2])
        }

    def find_connected_components(self, graph):
        """使用DFS查找连通分量，与参考代码逻辑一致"""
        def dfs(node, component):
            visited[node] = True
            component.append(node)

            for neighbor in graph[node]:
                if not visited[neighbor]:
                    dfs(neighbor, component)

        visited = [False] * len(graph)
        components = []

        for i in range(len(graph)):
            if not visited[i]:
                component = []
                dfs(i, component)
                components.append(component)

        return components

    def U_NumSemSets(self, semantic_matrix_entail: np.ndarray, semantic_matrix_contra: np.ndarray) -> tuple:
        """
        计算NumSets不确定性分数，完全按照参考代码逻辑实现
        返回 (不确定性分数, 连通分量列表)
        """
        # We have forward upper triangular and backward in lower triangular
        # parts of the semantic matrices
        W_entail = semantic_matrix_entail
        W_contra = semantic_matrix_contra

        # We check that for every pairing (both forward and backward)
        # the condition satisfies
        W = (W_entail > W_contra).astype(int)
        # Multiply by it's transpose to get symmetric matrix of full condition
        W = W * np.transpose(W)
        # Take upper triangular part with no diag
        W = np.triu(W, k=1)

        a = [[i] for i in range(W.shape[0])]

        # Iterate through each row in 'W' and update the corresponding row in 'a'
        for i, row in enumerate(W):
            # Find the indices of non-zero elements in the current row
            non_zero_indices = np.where(row != 0)[0]

            # Append the non-zero indices to the corresponding row in 'a'
            a[i].extend(non_zero_indices.tolist())

        # Create an adjacency list representation of the graph
        graph = [[] for _ in range(len(a))]
        for sublist in a:
            for i in range(len(sublist) - 1):
                graph[sublist[i]].append(sublist[i + 1])
                graph[sublist[i + 1]].append(sublist[i])

        # Find the connected components
        connected_components = self.find_connected_components(graph)

        # Calculate the number of connected components
        # Cast to float for consistency with other estimators
        num_components = float(len(connected_components))

        return num_components, connected_components

    def compute_uncertainty(self, responses: List[str]) -> Dict[str, Any]:
        """
        主流程：按照参考代码逻辑实现NumSets不确定性估计
        """
        n = len(responses)
        if n < 2:
            return {"error": "Need at least 2 responses"}

        if self.verbose:
            log.debug(f"generated answers: {responses}")

        # 计算语义矩阵
        matrices = self.compute_semantic_matrices(responses)
        semantic_matrix_entail = matrices['semantic_matrix_entail']
        semantic_matrix_contra = matrices['semantic_matrix_contra']

        # 使用参考代码的逻辑计算不确定性分数和连通分量
        uncertainty_score, connected_components = self.U_NumSemSets(semantic_matrix_entail, semantic_matrix_contra)

        # 为了兼容性，也计算一些额外的统计信息
        W_entail = semantic_matrix_entail
        W_contra = semantic_matrix_contra
        W = (W_entail > W_contra).astype(int)
        W = W * np.transpose(W)
        W = np.triu(W, k=1)

        # 构建每个语义集合包含的响应内容
        semantic_sets = []
        for component in connected_components:
            set_responses = [responses[idx] for idx in component]
            semantic_sets.append({
                "indices": component,
                "responses": set_responses,
                "size": len(component)
            })

        # 按集合大小排序（从大到小）
        semantic_sets.sort(key=lambda x: x["size"], reverse=True)

        # 结果组织
        result = {
            "uncertainty_score": uncertainty_score,
            "num_sets": int(uncertainty_score),  # 连通分量数量
            "semantic_sets": semantic_sets,  # 每个语义集合的详细信息
            "semantic_matrix_entail": semantic_matrix_entail.tolist(),
            "semantic_matrix_contra": semantic_matrix_contra.tolist(),
            "condition_matrix": W.tolist(),
            "responses": responses,
            "method": "num_sets",
            "nli_system": "unified" if self.use_unified_nli else "independent"
        }
        return result

    def get_required_samples(self) -> int:
        """返回所需的最小样本数"""
        return 2

    def __str__(self):
        return "NumSemSets"
