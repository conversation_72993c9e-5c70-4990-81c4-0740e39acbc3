#!/usr/bin/env python3
"""
测试脚本：验证修改后的 NumSetsUQ 类
参照 test_ecc_methods.py 的逻辑，从MongoDB提取真实数据进行测试
"""

import json
import sys
import os
from datetime import datetime, timezone
from typing import List, Dict, Any, Optional
from pymongo import MongoClient
from tqdm import tqdm

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from uq_methods.implementations.num_sets import NumSetsUQ

def get_sample_group(col, task_name: str, dataset_source: str, limit_responses: int = 40) -> Optional[Dict[str, Any]]:
    """
    Get a sample group with specified number of responses for a given task.
    Groups by prompt_seed - each seed should have exactly 40 responses for the same input.
    """
    # Find a prompt_seed with exactly the required number of responses
    pipeline = [
        {
            "$match": {
                "task_name": task_name,
                "dataset_source": dataset_source
            }
        },
        {
            "$group": {
                "_id": {
                    "task_name": "$task_name",
                    "dataset_source": "$dataset_source",
                    "prompt_seed": "$prompt_seed",
                },
                "count": {"$sum": 1},
                "docs": {"$push": "$$ROOT"}
            }
        },
        {
            "$match": {
                "count": {"$eq": limit_responses}  # Exactly 40 responses
            }
        },
        {
            "$limit": 1
        }
    ]

    result = list(col.aggregate(pipeline))
    if not result:
        print(f"No prompt_seed found for {task_name}/{dataset_source} with exactly {limit_responses} responses")
        return None

    group = result[0]
    docs = group["docs"]

    return {
        "group_id": group["_id"],
        "docs": docs,
        "count": len(docs)
    }

def build_group_key(doc: Dict[str, Any], group_id: Dict[str, Any]) -> Dict[str, Any]:
    """Build group key from a document and group_id."""
    return {
        "task_type": doc.get("task_name"),
        "dataset_source": doc.get("dataset_source"),
        "prompt_seed": group_id.get("prompt_seed"),
        "input_text": doc.get("input_text"),
    }

def extract_responses(docs: List[Dict[str, Any]], task_name: str) -> List[str]:
    """Extract responses from documents based on task type."""
    responses = []

    for doc in docs:
        if task_name == "sentiment_analysis":
            # Use parsed_answer for sentiment analysis
            response = doc.get("parsed_answer")
        elif task_name == "explorative_coding":
            # Use raw_answer for explorative coding
            response = doc.get("raw_answer")
        else:
            # Fallback: try parsed_answer first, then raw_answer
            response = doc.get("parsed_answer") or doc.get("raw_answer")

        if response:
            responses.append(str(response))

    return responses

def infer_reference_text(docs: List[Dict[str, Any]]) -> Optional[str]:
    """Infer reference text from documents."""
    try:
        refs = [d.get("reference_answer") for d in docs if d.get("reference_answer")]
        if refs:
            from collections import Counter
            return Counter(refs).most_common(1)[0][0]
    except Exception:
        pass
    return None

def test_num_sets_with_mongo_data():
    """使用MongoDB真实数据测试NumSets方法"""
    print("=== 测试 NumSets 方法（MongoDB真实数据）===")

    # 创建NumSets实例
    uq_method = NumSetsUQ(verbose=True)
    print(f"✓ 创建NumSets实例，NLI模型: {uq_method.model_name}")

    # 连接MongoDB
    try:
        client = MongoClient("localhost", 27017)
        db = client["LLM-UQ"]
        col = db["response_collections"]
        print(f"✓ 连接MongoDB成功")
    except Exception as e:
        print(f"❌ 连接MongoDB失败: {e}")
        return

    # 测试配置 - 参照test_ecc_methods.py
    test_configs = [
        {"task_name": "sentiment_analysis", "dataset_source": "twitter_sentiment"},
        {"task_name": "explorative_coding", "dataset_source": "pytorch_commits"},
        {"task_name": "explorative_coding", "dataset_source": "tensorflow_commits"},
    ]

    all_results = []

    for config in test_configs:
        task_name = config["task_name"]
        dataset_source = config["dataset_source"]

        print(f"\n--- 测试任务: {task_name} / {dataset_source} ---")

        # 获取样本组（40个响应）
        group_data = get_sample_group(col, task_name, dataset_source, limit_responses=40)

        if not group_data:
            print(f"❌ 未找到 {task_name}/{dataset_source} 的有效数据组")
            continue

        docs = group_data["docs"]
        group_id = group_data["group_id"]

        print(f"✓ 找到数据组，响应数量: {len(docs)}")
        print(f"  prompt_seed: {group_id.get('prompt_seed')}")

        # 提取响应
        responses = extract_responses(docs, task_name)

        if len(responses) < 2:
            print(f"❌ 有效响应数量不足: {len(responses)}")
            continue

        print(f"✓ 提取有效响应: {len(responses)} 个")

        # 显示前3个响应作为示例
        print("响应示例:")
        for i, response in enumerate(responses[:3]):
            print(f"  {i+1}. {response[:100]}{'...' if len(response) > 100 else ''}")

        try:
            # 计算不确定性
            result = uq_method.compute_uncertainty(responses)

            print(f"\n结果:")
            print(f"  不确定性分数: {result['uncertainty_score']:.4f}")
            print(f"  语义集合数量: {result['num_sets']}")
            print(f"  NLI系统: {result['nli_system']}")

            # 显示语义集合分类
            print(f"\n  语义集合分类:")
            for i, semantic_set in enumerate(result['semantic_sets']):
                print(f"    集合 {i+1} (大小: {semantic_set['size']}):")
                for j, response in enumerate(semantic_set['responses'][:2]):  # 只显示前2个
                    print(f"      {j+1}. {response[:80]}{'...' if len(response) > 80 else ''}")
                if len(semantic_set['responses']) > 2:
                    print(f"      ... 还有 {len(semantic_set['responses']) - 2} 个响应")

            # 构建组键
            group_key = build_group_key(docs[0], group_id)

            # 保存结果
            all_results.append({
                'task_name': task_name,
                'dataset_source': dataset_source,
                'prompt_seed': group_id.get('prompt_seed'),
                'num_responses': len(responses),
                'uncertainty_score': result['uncertainty_score'],
                'num_sets': result['num_sets'],
                'nli_system': result['nli_system'],
                'group_key': group_key
            })

        except Exception as e:
            print(f"❌ 测试组 {task_name}/{dataset_source} 失败: {str(e)}")
            import traceback
            traceback.print_exc()

    # 关闭MongoDB连接
    client.close()

    # 总结结果
    print(f"\n=== 测试总结 ===")
    print(f"成功测试组数: {len(all_results)}")

    if all_results:
        uncertainty_scores = [r['uncertainty_score'] for r in all_results]
        num_sets_values = [r['num_sets'] for r in all_results]

        print(f"不确定性分数范围: {min(uncertainty_scores):.4f} - {max(uncertainty_scores):.4f}")
        print(f"语义集合数量范围: {min(num_sets_values)} - {max(num_sets_values)}")
        print(f"平均不确定性分数: {sum(uncertainty_scores)/len(uncertainty_scores):.4f}")
        print(f"平均语义集合数量: {sum(num_sets_values)/len(num_sets_values):.2f}")

        print(f"\n详细结果:")
        for result in all_results:
            print(f"  {result['task_name']}/{result['dataset_source']}: "
                  f"响应={result['num_responses']}, "
                  f"不确定性={result['uncertainty_score']:.4f}, "
                  f"集合数={result['num_sets']}")

    return all_results

def test_edge_cases():
    """测试边界情况"""
    print("\n=== 测试边界情况 ===")

    uq_method = NumSetsUQ()

    # 测试单个响应
    print("1. 单个响应:")
    result = uq_method.compute_uncertainty(["Only one response"])
    print(f"   错误信息: {result.get('error', 'None')}")

    # 测试空响应
    print("2. 空响应:")
    result = uq_method.compute_uncertainty([])
    print(f"   错误信息: {result.get('error', 'None')}")

    # 测试相同响应
    print("3. 相同响应:")
    result = uq_method.compute_uncertainty(["Same response"] * 5)
    print(f"   不确定性分数: {result['uncertainty_score']:.4f}")
    print(f"   语义集合数量: {result['num_sets']}")

if __name__ == "__main__":
    print("开始测试修改后的 NumSetsUQ 类...")
    print("参照 test_ecc_methods.py 的逻辑，从MongoDB提取真实数据进行测试")
    print()

    try:
        test_num_sets_with_mongo_data()
        test_edge_cases()

        print("\n✅ 所有测试完成！")

    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()


