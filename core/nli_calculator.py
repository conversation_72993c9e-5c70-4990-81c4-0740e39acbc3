"""
NLI计算模块 - 统一的自然语言推理计算和缓存管理

该模块提供：
1. 统一的NLI模型加载和计算
2. 完整的三分数计算 (entailment, neutral, contradiction)
3. 缓存管理
4. 向后兼容性
"""

import torch
import numpy as np
from transformers import AutoTokenizer, AutoModelForSequenceClassification
from typing import List, Tuple, NamedTuple, Optional
import hashlib
import time
import logging
import os
import pandas as pd

logger = logging.getLogger(__name__)

class NLIResult(NamedTuple):
    """NLI计算结果，包含三个分数"""
    entailment: float
    neutral: float
    contradiction: float


class NLICalculator:
    """统一的NLI计算器"""
    
    def __init__(self, model_name: str = "microsoft/deberta-large-mnli", verbose: bool = False):
        """
        初始化NLI计算器
        
        Args:
            model_name: NLI模型名称
            verbose: 是否输出详细信息
        """
        self.model_name = model_name
        self.verbose = verbose
        
        # 初始化设备和模型
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        if self.verbose:
            logger.info(f"Loading NLI model: {model_name} on {self.device}")
        
        self.tokenizer = AutoTokenizer.from_pretrained(model_name)
        self.model = AutoModelForSequenceClassification.from_pretrained(model_name).to(self.device)
        self.model.eval()
        
        if self.verbose:
            logger.info(f"NLI model loaded successfully")
    
    def compute_nli_scores(self, text1: str, text2: str) -> NLIResult:
        """
        计算两个文本之间的完整NLI分数
        
        Args:
            text1: 前提文本
            text2: 假设文本
            
        Returns:
            NLIResult: 包含entailment, neutral, contradiction三个分数
        """
        try:
            inputs = self.tokenizer(
                text1, text2, 
                return_tensors="pt", 
                truncation=True, 
                max_length=256
            ).to(self.device)
            
            with torch.no_grad():
                logits = self.model(**inputs).logits
                probs = torch.softmax(logits, dim=-1).cpu().numpy()[0]
            
            # Label mapping: 0=contradiction, 1=neutral, 2=entailment
            contradiction_score = float(probs[0])
            neutral_score = float(probs[1])
            entailment_score = float(probs[2])
            
            return NLIResult(
                entailment=entailment_score,
                neutral=neutral_score,
                contradiction=contradiction_score
            )
            
        except Exception as e:
            logger.warning(f"Error computing NLI scores for model {self.model_name}: {str(e)}")
            # 返回均匀分布作为默认值
            return NLIResult(entailment=0.33, neutral=0.34, contradiction=0.33)
    
    def compute_entailment_score(self, text1: str, text2: str) -> float:
        """
        计算entailment分数（向后兼容）
        
        Args:
            text1: 前提文本
            text2: 假设文本
            
        Returns:
            float: entailment分数
        """
        nli_result = self.compute_nli_scores(text1, text2)
        return nli_result.entailment
    
    def compute_similarity_matrix(self, responses: List[str], use_score: str = "entailment") -> np.ndarray:
        """
        计算响应列表的相似度矩阵
        
        Args:
            responses: 响应文本列表
            use_score: 使用的分数类型 ("entailment", "neutral", "contradiction")
            
        Returns:
            numpy.ndarray: 相似度矩阵
        """
        n = len(responses)
        W = np.zeros((n, n))
        
        for i in range(n):
            for j in range(n):
                if i == j:
                    W[i, j] = 1.0
                else:
                    # 计算双向分数并取平均
                    nli_ij = self.compute_nli_scores(responses[i], responses[j])
                    nli_ji = self.compute_nli_scores(responses[j], responses[i])
                    
                    # 根据指定的分数类型获取值
                    if use_score == "entailment":
                        score_ij = nli_ij.entailment
                        score_ji = nli_ji.entailment
                    elif use_score == "neutral":
                        score_ij = nli_ij.neutral
                        score_ji = nli_ji.neutral
                    elif use_score == "contradiction":
                        score_ij = nli_ij.contradiction
                        score_ji = nli_ji.contradiction
                    else:
                        raise ValueError(f"Unknown score type: {use_score}")
                    
                    W[i, j] = (score_ij + score_ji) / 2
        
        # 确保矩阵对称
        W = (W + W.T) / 2
        return W
    
    @staticmethod
    def get_text_hash(text: str) -> str:
        """生成文本的哈希值"""
        return hashlib.md5(text.encode('utf-8')).hexdigest()
    
    def get_cache_key(self, text1: str, text2: str) -> str:
        """生成缓存键"""
        text1_hash = self.get_text_hash(text1)
        text2_hash = self.get_text_hash(text2)
        return f"{text1_hash}||{text2_hash}||{self.model_name}"


class CachedNLICalculator(NLICalculator):
    """带缓存的NLI计算器 - 支持CSV文件持久化缓存"""

    def __init__(self, model_name: str = "microsoft/deberta-large-mnli", verbose: bool = False):
        super().__init__(model_name, verbose)
        self.nli_cache = {}  # 内存缓存

        # CSV文件缓存配置
        self.cache_dir = "cache"
        self.csv_cache_file = os.path.join(self.cache_dir, "nli_results_cache.csv")
        self.csv_cache = {}  # CSV缓存的内存副本

        # 创建缓存目录
        os.makedirs(self.cache_dir, exist_ok=True)

        # 加载CSV缓存
        self._load_csv_cache()
        
    def _load_csv_cache(self):
        """加载CSV格式的NLI缓存"""
        if os.path.exists(self.csv_cache_file):
            try:
                df = pd.read_csv(self.csv_cache_file)
                if self.verbose:
                    logger.info(f"Loading NLI CSV cache from {self.csv_cache_file} with {len(df)} rows")

                # 将DataFrame转换为字典格式
                self.csv_cache = {}
                for _, row in df.iterrows():
                    try:
                        text1_hash = row.get('text1_hash', self.get_text_hash(row['text1']))
                        text2_hash = row.get('text2_hash', self.get_text_hash(row['text2']))
                        cache_key = f"{text1_hash}||{text2_hash}||{row['model_name']}"

                        self.csv_cache[cache_key] = {
                            'text1': row['text1'],
                            'text2': row['text2'],
                            'model_name': row['model_name'],
                            'entailment': float(row['entailment']),
                            'neutral': float(row['neutral']),
                            'contradiction': float(row['contradiction']),
                            'text1_hash': text1_hash,
                            'text2_hash': text2_hash,
                            'timestamp': row.get('timestamp', '')
                        }
                    except Exception as e:
                        if self.verbose:
                            logger.warning(f"Error processing cache row: {e}")
                        continue

                if self.verbose:
                    logger.info(f"Loaded {len(self.csv_cache)} entries from CSV cache")

            except Exception as e:
                logger.error(f"Failed to load NLI CSV cache: {e}")
                self.csv_cache = {}
        else:
            if self.verbose:
                logger.info(f"NLI CSV cache file {self.csv_cache_file} does not exist, starting with empty cache")
            self.csv_cache = {}

    def _save_csv_cache(self):
        """保存CSV格式的NLI缓存"""
        try:
            if not self.csv_cache:
                return

            # 转换字典为DataFrame
            rows = []
            for cache_key, data in self.csv_cache.items():
                rows.append({
                    'text1': data['text1'],
                    'text2': data['text2'],
                    'model_name': data['model_name'],
                    'entailment': data['entailment'],
                    'neutral': data['neutral'],
                    'contradiction': data['contradiction'],
                    'text1_hash': data['text1_hash'],
                    'text2_hash': data['text2_hash'],
                    'timestamp': data['timestamp']
                })

            df = pd.DataFrame(rows)
            df.to_csv(self.csv_cache_file, index=False, encoding='utf-8-sig')

            if self.verbose:
                logger.info(f"Saved NLI CSV cache with {len(rows)} entries to {self.csv_cache_file}")

        except Exception as e:
            logger.warning(f"Failed to save NLI CSV cache: {e}")

    def compute_nli_scores_cached(self, text1: str, text2: str) -> NLIResult:
        """
        带缓存的NLI分数计算 - 支持CSV文件持久化缓存

        Args:
            text1: 前提文本
            text2: 假设文本

        Returns:
            NLIResult: 包含entailment, neutral, contradiction三个分数
        """
        cache_key = self.get_cache_key(text1, text2)

        # 1. 首先检查内存缓存
        if cache_key in self.nli_cache:
            return self.nli_cache[cache_key]

        # 2. 检查CSV缓存
        if cache_key in self.csv_cache:
            cached_data = self.csv_cache[cache_key]
            nli_result = NLIResult(
                entailment=cached_data['entailment'],
                neutral=cached_data['neutral'],
                contradiction=cached_data['contradiction']
            )
            # 同时缓存到内存
            self.nli_cache[cache_key] = nli_result
            return nli_result

        # 3. 计算新的NLI分数
        if self.verbose:
            logger.info(f"Computing new NLI scores for model {self.model_name}")
        nli_result = self.compute_nli_scores(text1, text2)

        # 4. 缓存到内存
        self.nli_cache[cache_key] = nli_result

        # 5. 缓存到CSV
        timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
        text1_hash = self.get_text_hash(text1)
        text2_hash = self.get_text_hash(text2)

        self.csv_cache[cache_key] = {
            'text1': text1,
            'text2': text2,
            'model_name': self.model_name,
            'entailment': nli_result.entailment,
            'neutral': nli_result.neutral,
            'contradiction': nli_result.contradiction,
            'text1_hash': text1_hash,
            'text2_hash': text2_hash,
            'timestamp': timestamp
        }

        # 6. 定期保存CSV缓存（每10个新条目保存一次）
        if len(self.csv_cache) % 10 == 0:
            self._save_csv_cache()

        return nli_result
    
    def compute_similarity_matrix_cached(self, responses: List[str], use_score: str = "entailment") -> np.ndarray:
        """计算响应列表的缓存相似度矩阵"""
        n = len(responses)
        W = np.zeros((n, n))
        
        for i in range(n):
            for j in range(n):
                if i == j:
                    W[i, j] = 1.0
                else:
                    # 使用缓存计算双向分数并取平均
                    nli_ij = self.compute_nli_scores_cached(responses[i], responses[j])
                    nli_ji = self.compute_nli_scores_cached(responses[j], responses[i])
                    
                    # 根据指定的分数类型获取值
                    if use_score == "entailment":
                        score_ij = nli_ij.entailment
                        score_ji = nli_ji.entailment
                    elif use_score == "neutral":
                        score_ij = nli_ij.neutral
                        score_ji = nli_ji.neutral
                    elif use_score == "contradiction":
                        score_ij = nli_ij.contradiction
                        score_ji = nli_ji.contradiction
                    else:
                        raise ValueError(f"Unknown score type: {use_score}")
                    
                    W[i, j] = (score_ij + score_ji) / 2
        
        # 确保矩阵对称
        W = (W + W.T) / 2
        return W
    
    def get_cache_stats(self) -> dict:
        """获取缓存统计信息"""
        return {
            'memory_cache_entries': len(self.nli_cache),
            'csv_cache_entries': len(self.csv_cache),
            'model_name': self.model_name,
            'csv_cache_file': self.csv_cache_file
        }

    def clear_cache(self):
        """清空内存缓存"""
        self.nli_cache.clear()
        if self.verbose:
            logger.info("NLI memory cache cleared")

    def clear_all_cache(self):
        """清空所有缓存（包括CSV文件）"""
        self.nli_cache.clear()
        self.csv_cache.clear()
        if os.path.exists(self.csv_cache_file):
            os.remove(self.csv_cache_file)
        if self.verbose:
            logger.info("All NLI caches cleared")

    def save_cache(self):
        """手动保存CSV缓存"""
        self._save_csv_cache()

    def __del__(self):
        """析构函数：确保缓存被保存"""
        try:
            self._save_csv_cache()
        except:
            pass
