#!/usr/bin/env python3
"""
详细测试脚本：验证 NumSetsUQ 类的完整功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_simple_case():
    """测试简单情况"""
    print("=== 测试简单情况 ===")
    
    try:
        from uq_methods.implementations.num_sets import NumSetsUQ
        
        uq_method = NumSetsUQ(use_unified_nli=True, verbose=False)
        
        # 简单测试：3个明显不同的响应
        responses = [
            "The cat is sleeping.",
            "Mathematics is difficult.",
            "I love pizza."
        ]
        
        result = uq_method.compute_uncertainty(responses)
        
        print(f"输入: {len(responses)} 个完全不同的响应")
        print(f"结果: {result['num_sets']} 个语义集合")
        print(f"不确定性分数: {result['uncertainty_score']:.4f}")
        
        print("\n语义集合:")
        for i, semantic_set in enumerate(result['semantic_sets']):
            print(f"  集合 {i+1}: {semantic_set['responses']}")
        
        print(f"✅ 预期: 3个集合, 实际: {result['num_sets']}个集合")
        print()
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")

def test_identical_responses():
    """测试相同响应"""
    print("=== 测试相同响应 ===")
    
    try:
        from uq_methods.implementations.num_sets import NumSetsUQ
        
        uq_method = NumSetsUQ(use_unified_nli=True, verbose=False)
        
        # 相同响应
        responses = ["The cat is sleeping."] * 4
        
        result = uq_method.compute_uncertainty(responses)
        
        print(f"输入: {len(responses)} 个相同响应")
        print(f"结果: {result['num_sets']} 个语义集合")
        print(f"不确定性分数: {result['uncertainty_score']:.4f}")
        
        print("\n语义集合:")
        for i, semantic_set in enumerate(result['semantic_sets']):
            print(f"  集合 {i+1} (大小: {semantic_set['size']}): {semantic_set['responses'][0]}")
        
        print(f"✅ 预期: 1个集合, 实际: {result['num_sets']}个集合")
        print()
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")

def test_semantic_groups():
    """测试语义分组"""
    print("=== 测试语义分组 ===")
    
    try:
        from uq_methods.implementations.num_sets import NumSetsUQ
        
        uq_method = NumSetsUQ(use_unified_nli=True, verbose=False)
        
        # 设计包含2个语义组的响应
        responses = [
            # 组1: 动物睡觉
            "The cat is sleeping on the mat.",
            "A feline is resting on a rug.",
            "The kitty is napping on the carpet.",
            # 组2: 动物跑步
            "The dog is running in the park.",
            "A canine is jogging through the garden.",
            "The puppy is sprinting in the yard."
        ]
        
        result = uq_method.compute_uncertainty(responses)
        
        print(f"输入: {len(responses)} 个响应 (设计为2个语义组)")
        print(f"结果: {result['num_sets']} 个语义集合")
        print(f"不确定性分数: {result['uncertainty_score']:.4f}")
        
        print("\n语义集合分类:")
        for i, semantic_set in enumerate(result['semantic_sets']):
            print(f"\n集合 {i+1} (大小: {semantic_set['size']}):")
            for j, response in enumerate(semantic_set['responses']):
                print(f"  {j+1}. {response}")
        
        print(f"\n✅ 预期: 2个集合, 实际: {result['num_sets']}个集合")
        print()
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")

def test_matrix_analysis():
    """测试矩阵分析"""
    print("=== 测试矩阵分析 ===")
    
    try:
        from uq_methods.implementations.num_sets import NumSetsUQ
        import numpy as np
        
        uq_method = NumSetsUQ(use_unified_nli=True, verbose=False)
        
        # 简单的3个响应
        responses = [
            "The cat is sleeping.",
            "A cat is resting.",
            "The dog is running."
        ]
        
        result = uq_method.compute_uncertainty(responses)
        
        print(f"输入: {len(responses)} 个响应")
        print(f"结果: {result['num_sets']} 个语义集合")
        
        # 分析矩阵
        entail_matrix = np.array(result['semantic_matrix_entail'])
        contra_matrix = np.array(result['semantic_matrix_contra'])
        condition_matrix = np.array(result['condition_matrix'])
        
        print(f"\nEntailment Matrix:")
        for i, row in enumerate(entail_matrix):
            print(f"  Row {i}: {[f'{x:.3f}' for x in row]}")
        
        print(f"\nContradiction Matrix:")
        for i, row in enumerate(contra_matrix):
            print(f"  Row {i}: {[f'{x:.3f}' for x in row]}")
        
        print(f"\nCondition Matrix (entail > contra):")
        for i, row in enumerate(condition_matrix):
            print(f"  Row {i}: {[int(x) for x in row]}")
        
        print("\n语义集合:")
        for i, semantic_set in enumerate(result['semantic_sets']):
            print(f"  集合 {i+1}: {semantic_set['responses']}")
        
        print("✅ 矩阵分析完成")
        print()
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")

def test_edge_cases():
    """测试边界情况"""
    print("=== 测试边界情况 ===")
    
    try:
        from uq_methods.implementations.num_sets import NumSetsUQ
        
        uq_method = NumSetsUQ(use_unified_nli=True, verbose=False)
        
        # 测试单个响应
        print("1. 单个响应:")
        result = uq_method.compute_uncertainty(["Only one response"])
        print(f"   错误信息: {result.get('error', 'None')}")
        
        # 测试空响应
        print("2. 空响应:")
        result = uq_method.compute_uncertainty([])
        print(f"   错误信息: {result.get('error', 'None')}")
        
        print("✅ 边界情况测试完成")
        print()
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")

if __name__ == "__main__":
    print("开始详细测试 NumSetsUQ 类...")
    print()
    
    test_simple_case()
    test_identical_responses()
    test_semantic_groups()
    test_matrix_analysis()
    test_edge_cases()
    
    print("🎉 所有测试完成！")
