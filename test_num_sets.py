#!/usr/bin/env python3
"""
测试脚本：验证修改后的 NumSetsUQ 类
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from uq_methods.implementations.num_sets import NumSetsUQ

def test_num_sets_unified():
    """测试 NumSets 统一NLI模式"""
    print("=== 测试 NumSets 统一NLI模式 ===")
    
    # 创建实例
    uq_method = NumSetsUQ(use_unified_nli=True, verbose=True)
    
    # 测试数据 - 包含语义相似和不相似的响应
    responses = [
        "The cat is sleeping on the mat.",
        "A cat is resting on a rug.",
        "The dog is running in the park.",
        "A canine is jogging through the garden.",
        "The weather is sunny today."
    ]
    
    # 计算不确定性
    result = uq_method.compute_uncertainty(responses)
    
    print(f"Method: {result['method']}")
    print(f"Uncertainty Score (Num Sets): {result['uncertainty_score']:.4f}")
    print(f"Number of Semantic Sets: {result['num_sets']}")
    print(f"NLI System: {result['nli_system']}")
    print(f"Responses Count: {len(result['responses'])}")

    # 显示语义集合分类结果
    print("\n语义集合分类结果:")
    for i, semantic_set in enumerate(result['semantic_sets']):
        print(f"  集合 {i+1} (大小: {semantic_set['size']}):")
        for j, response in enumerate(semantic_set['responses']):
            print(f"    {j+1}. {response}")
    print()

def test_num_sets_independent():
    """测试 NumSets 独立NLI模式"""
    print("=== 测试 NumSets 独立NLI模式 ===")
    
    # 创建实例
    uq_method = NumSetsUQ(use_unified_nli=False, verbose=True)
    
    # 测试数据
    responses = [
        "The cat is sleeping on the mat.",
        "A cat is resting on a rug.",
        "The dog is running in the park.",
        "A canine is jogging through the garden.",
        "The weather is sunny today."
    ]
    
    # 计算不确定性
    result = uq_method.compute_uncertainty(responses)
    
    print(f"Method: {result['method']}")
    print(f"Uncertainty Score (Num Sets): {result['uncertainty_score']:.4f}")
    print(f"Number of Semantic Sets: {result['num_sets']}")
    print(f"NLI System: {result['nli_system']}")
    print(f"Responses Count: {len(result['responses'])}")

    # 显示语义集合分类结果
    print("\n语义集合分类结果:")
    for i, semantic_set in enumerate(result['semantic_sets']):
        print(f"  集合 {i+1} (大小: {semantic_set['size']}):")
        for j, response in enumerate(semantic_set['responses']):
            print(f"    {j+1}. {response}")
    print()

def test_semantic_matrices():
    """测试语义矩阵计算"""
    print("=== 测试语义矩阵计算 ===")
    
    uq_method = NumSetsUQ(use_unified_nli=True, verbose=True)
    
    # 简单测试数据
    responses = [
        "The cat is sleeping.",
        "A cat is resting.",
        "The dog is running."
    ]
    
    result = uq_method.compute_uncertainty(responses)
    
    print(f"Semantic Matrix Entail shape: {len(result['semantic_matrix_entail'])}x{len(result['semantic_matrix_entail'][0])}")
    print(f"Semantic Matrix Contra shape: {len(result['semantic_matrix_contra'])}x{len(result['semantic_matrix_contra'][0])}")
    print(f"Condition Matrix shape: {len(result['condition_matrix'])}x{len(result['condition_matrix'][0])}")
    
    # 显示矩阵内容（小矩阵）
    print("\nEntailment Matrix:")
    for i, row in enumerate(result['semantic_matrix_entail']):
        print(f"  Row {i}: {[f'{x:.3f}' for x in row]}")
    
    print("\nContradiction Matrix:")
    for i, row in enumerate(result['semantic_matrix_contra']):
        print(f"  Row {i}: {[f'{x:.3f}' for x in row]}")
    
    print("\nCondition Matrix (entail > contra):")
    for i, row in enumerate(result['condition_matrix']):
        print(f"  Row {i}: {row}")
    print()

def test_edge_cases():
    """测试边界情况"""
    print("=== 测试边界情况 ===")
    
    uq_method = NumSetsUQ(use_unified_nli=True)
    
    # 测试单个响应
    single_response = ["Only one response"]
    result = uq_method.compute_uncertainty(single_response)
    print(f"单个响应 - Error: {result.get('error', 'None')}")
    
    # 测试空响应
    empty_responses = []
    result = uq_method.compute_uncertainty(empty_responses)
    print(f"空响应 - Error: {result.get('error', 'None')}")
    
    # 测试相同响应
    identical_responses = ["Same response"] * 3
    result = uq_method.compute_uncertainty(identical_responses)
    print(f"相同响应 - Uncertainty: {result['uncertainty_score']:.4f}, Num Sets: {result['num_sets']}")
    
    # 测试完全不同的响应
    different_responses = [
        "The cat is sleeping.",
        "Mathematics is difficult.",
        "I love pizza.",
        "The sky is blue."
    ]
    result = uq_method.compute_uncertainty(different_responses)
    print(f"完全不同响应 - Uncertainty: {result['uncertainty_score']:.4f}, Num Sets: {result['num_sets']}")
    print()

def test_semantic_similarity_groups():
    """测试语义相似性分组"""
    print("=== 测试语义相似性分组 ===")
    
    uq_method = NumSetsUQ(use_unified_nli=True, verbose=True)
    
    # 设计包含明确语义组的测试数据
    responses = [
        # 组1: 猫相关
        "The cat is sleeping on the mat.",
        "A feline is resting on a rug.",
        # 组2: 狗相关  
        "The dog is running in the park.",
        "A canine is jogging through the garden.",
        # 组3: 天气相关
        "The weather is sunny today.",
        "It's a bright and clear day.",
        # 组4: 独立响应
        "Mathematics is a complex subject."
    ]
    
    result = uq_method.compute_uncertainty(responses)
    
    print(f"Total Responses: {len(responses)}")
    print(f"Number of Semantic Sets: {result['num_sets']}")
    print(f"Uncertainty Score: {result['uncertainty_score']:.4f}")

    # 显示语义集合分类结果
    print("\n语义集合分类结果:")
    for i, semantic_set in enumerate(result['semantic_sets']):
        print(f"  集合 {i+1} (大小: {semantic_set['size']}):")
        for j, response in enumerate(semantic_set['responses']):
            print(f"    {j+1}. {response}")

    # 分析条件矩阵
    condition_matrix = result['condition_matrix']
    print("\nCondition Matrix (upper triangular):")
    for i, row in enumerate(condition_matrix):
        print(f"  Row {i}: {row}")

    print(f"\nExpected: 应该识别出3-4个语义集合")
    print()

def test_algorithm_consistency():
    """测试算法一致性 - 验证与参考代码逻辑的一致性"""
    print("=== 测试算法一致性 ===")
    
    uq_method = NumSetsUQ(use_unified_nli=True, verbose=True)
    
    # 简单的测试用例，便于验证逻辑
    responses = [
        "The cat is sleeping.",
        "A cat is resting.",
        "The dog is running."
    ]
    
    result = uq_method.compute_uncertainty(responses)
    
    # 验证关键步骤
    entail_matrix = result['semantic_matrix_entail']
    contra_matrix = result['semantic_matrix_contra']
    condition_matrix = result['condition_matrix']
    
    print("验证算法步骤:")
    print("1. 语义矩阵计算 ✓")
    print("2. 条件矩阵 (entail > contra) ✓")
    print("3. 对称化处理 ✓")
    print("4. 上三角提取 ✓")
    print("5. 连通分量计算 ✓")
    
    print(f"\n最终结果: {result['num_sets']} 个语义集合")
    print(f"不确定性分数: {result['uncertainty_score']:.4f}")
    print()

if __name__ == "__main__":
    print("开始测试修改后的 NumSetsUQ 类...")
    print()
    
    try:
        test_num_sets_unified()
        test_num_sets_independent()
        test_semantic_matrices()
        test_edge_cases()
        test_semantic_similarity_groups()
        test_algorithm_consistency()
        
        print("✅ 所有测试完成！")
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
