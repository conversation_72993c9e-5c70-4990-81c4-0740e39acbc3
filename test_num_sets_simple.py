#!/usr/bin/env python3
"""
简单测试脚本：验证 NumSetsUQ 类的语义集合分类功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_num_sets_classification():
    """测试语义集合分类功能"""
    print("=== 测试 NumSets 语义集合分类 ===")
    
    try:
        from uq_methods.implementations.num_sets import NumSetsUQ
        
        # 创建实例
        uq_method = NumSetsUQ(use_unified_nli=True, verbose=False)
        
        # 测试数据 - 设计包含明确语义组的响应
        responses = [
            "The cat is sleeping on the mat.",
            "A feline is resting on a rug.",
            "The dog is running in the park.",
            "A canine is jogging through the garden.",
            "The weather is sunny today.",
            "It's a bright and clear day."
        ]
        
        print(f"输入响应数量: {len(responses)}")
        print("\n输入响应:")
        for i, response in enumerate(responses):
            print(f"  {i+1}. {response}")
        
        # 计算不确定性
        result = uq_method.compute_uncertainty(responses)
        
        print(f"\n=== 结果 ===")
        print(f"不确定性分数: {result['uncertainty_score']:.4f}")
        print(f"语义集合数量: {result['num_sets']}")
        print(f"NLI系统: {result['nli_system']}")
        
        # 显示语义集合分类结果
        print(f"\n=== 语义集合分类结果 ===")
        for i, semantic_set in enumerate(result['semantic_sets']):
            print(f"\n集合 {i+1} (大小: {semantic_set['size']}):")
            for j, response in enumerate(semantic_set['responses']):
                print(f"  {j+1}. {response}")
        
        print(f"\n✅ 测试完成！识别出 {result['num_sets']} 个语义集合")
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_num_sets_classification()
