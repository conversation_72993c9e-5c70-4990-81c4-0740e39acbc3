#!/usr/bin/env python3
"""
测试脚本：验证修改后的 NumSetsUQ 类
参照 test_uq_methods.py 的逻辑，使用真实数据进行测试
"""

import sys
import os
import pandas as pd
import numpy as np
from typing import List, Dict, Any
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from uq_methods.implementations.num_sets import NumSetsUQ

def load_real_data() -> Dict[str, List[str]]:
    """加载真实数据进行测试"""
    test_groups = {}

    # 加载Twitter数据
    try:
        twitter_df = pd.read_csv('data/all_twitter_responses.csv')
        print(f"✓ 加载Twitter数据: {len(twitter_df)} 条记录")

        # 按tweet_index和prompt_type分组，取前几组进行测试
        twitter_groups = twitter_df.groupby(['tweet_index', 'prompt_type'])
        for i, ((tweet_idx, prompt_type), group) in enumerate(twitter_groups):
            if i >= 3:  # 只取前3组
                break
            responses = group['response_text'].dropna().tolist()
            if len(responses) >= 5:  # 确保有足够的响应
                group_key = f"twitter_{tweet_idx}_{prompt_type}"
                test_groups[group_key] = responses[:10]  # 取前10个响应
                print(f"  - 组 {group_key}: {len(test_groups[group_key])} 个响应")

    except FileNotFoundError:
        print("✗ 未找到Twitter数据文件")

    # 加载Commit数据
    try:
        commit_df = pd.read_csv('data/all_commit_responses.csv')
        print(f"✓ 加载Commit数据: {len(commit_df)} 条记录")

        # 按commit_sha和prompt_type分组，取前几组进行测试
        commit_groups = commit_df.groupby(['commit_sha', 'prompt_type'])
        for i, ((commit_sha, prompt_type), group) in enumerate(commit_groups):
            if i >= 3:  # 只取前3组
                break
            responses = group['response_text'].dropna().tolist()
            if len(responses) >= 5:  # 确保有足够的响应
                group_key = f"commit_{commit_sha[:8]}_{prompt_type}"
                test_groups[group_key] = responses[:10]  # 取前10个响应
                print(f"  - 组 {group_key}: {len(test_groups[group_key])} 个响应")

    except FileNotFoundError:
        print("✗ 未找到Commit数据文件")

    return test_groups

def test_num_sets_with_real_data():
    """使用真实数据测试NumSets方法"""
    print("=== 测试 NumSets 方法（真实数据）===")

    # 创建NumSets实例
    uq_method = NumSetsUQ(verbose=True)
    print(f"✓ 创建NumSets实例，NLI模型: {uq_method.model_name}")

    # 加载真实数据
    test_groups = load_real_data()

    if not test_groups:
        print("❌ 没有找到可用的测试数据")
        return

    print(f"\n开始测试 {len(test_groups)} 个数据组...")

    all_results = []

    for group_name, responses in test_groups.items():
        print(f"\n--- 测试组: {group_name} ---")
        print(f"响应数量: {len(responses)}")

        # 显示前3个响应作为示例
        print("响应示例:")
        for i, response in enumerate(responses[:3]):
            print(f"  {i+1}. {response[:100]}{'...' if len(response) > 100 else ''}")

        try:
            # 计算不确定性
            result = uq_method.compute_uncertainty(responses)

            print(f"\n结果:")
            print(f"  不确定性分数: {result['uncertainty_score']:.4f}")
            print(f"  语义集合数量: {result['num_sets']}")
            print(f"  NLI系统: {result['nli_system']}")

            # 显示语义集合分类
            print(f"\n  语义集合分类:")
            for i, semantic_set in enumerate(result['semantic_sets']):
                print(f"    集合 {i+1} (大小: {semantic_set['size']}):")
                for j, response in enumerate(semantic_set['responses'][:2]):  # 只显示前2个
                    print(f"      {j+1}. {response[:80]}{'...' if len(response) > 80 else ''}")
                if len(semantic_set['responses']) > 2:
                    print(f"      ... 还有 {len(semantic_set['responses']) - 2} 个响应")

            # 保存结果
            all_results.append({
                'group_name': group_name,
                'num_responses': len(responses),
                'uncertainty_score': result['uncertainty_score'],
                'num_sets': result['num_sets'],
                'nli_system': result['nli_system']
            })

        except Exception as e:
            print(f"❌ 测试组 {group_name} 失败: {str(e)}")
            import traceback
            traceback.print_exc()

    # 总结结果
    print(f"\n=== 测试总结 ===")
    print(f"成功测试组数: {len(all_results)}")

    if all_results:
        uncertainty_scores = [r['uncertainty_score'] for r in all_results]
        num_sets_values = [r['num_sets'] for r in all_results]

        print(f"不确定性分数范围: {min(uncertainty_scores):.4f} - {max(uncertainty_scores):.4f}")
        print(f"语义集合数量范围: {min(num_sets_values)} - {max(num_sets_values)}")
        print(f"平均不确定性分数: {np.mean(uncertainty_scores):.4f}")
        print(f"平均语义集合数量: {np.mean(num_sets_values):.2f}")

        print(f"\n详细结果:")
        for result in all_results:
            print(f"  {result['group_name']}: "
                  f"响应={result['num_responses']}, "
                  f"不确定性={result['uncertainty_score']:.4f}, "
                  f"集合数={result['num_sets']}")

def test_edge_cases():
    """测试边界情况"""
    print("\n=== 测试边界情况 ===")

    uq_method = NumSetsUQ()

    # 测试单个响应
    print("1. 单个响应:")
    result = uq_method.compute_uncertainty(["Only one response"])
    print(f"   错误信息: {result.get('error', 'None')}")

    # 测试空响应
    print("2. 空响应:")
    result = uq_method.compute_uncertainty([])
    print(f"   错误信息: {result.get('error', 'None')}")

    # 测试相同响应
    print("3. 相同响应:")
    result = uq_method.compute_uncertainty(["Same response"] * 5)
    print(f"   不确定性分数: {result['uncertainty_score']:.4f}")
    print(f"   语义集合数量: {result['num_sets']}")

if __name__ == "__main__":
    print("开始测试修改后的 NumSetsUQ 类...")
    print("参照 test_uq_methods.py 的逻辑，使用真实数据进行测试")
    print()

    try:
        test_num_sets_with_real_data()
        test_edge_cases()

        print("\n✅ 所有测试完成！")

    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()


